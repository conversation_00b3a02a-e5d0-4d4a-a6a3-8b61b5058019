import { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import GitHubProvider from 'next-auth/providers/github';

// TypeScript interface for Google OAuth profile
interface GoogleProfile {
  sub: string;
  name: string;
  given_name?: string;
  family_name?: string;
  picture?: string;
  email: string;
  email_verified: boolean;
  locale?: string;
}

// TypeScript interface for GitHub OAuth profile
interface GitHubProfile {
  id: number;
  login: string;
  name: string | null;
  email: string | null;
  avatar_url: string;
  bio?: string | null;
  location?: string | null;
  company?: string | null;
}

const googleClientId = process.env.GOOGLE_CLIENT_ID || '';
const googleClientSecret = process.env.GOOGLE_CLIENT_SECRET || '';
const githubClientId = process.env.GITHUB_ID || '';
const githubClientSecret = process.env.GITHUB_SECRET || '';

if (!googleClientId || !googleClientSecret) {
  throw new Error('Missing Google OAuth credentials');
}

if (!githubClientId || !githubClientSecret) {
  throw new Error('Missing GitHub OAuth credentials');
}

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: googleClientId,
      clientSecret: googleClientSecret,
    }),
    GitHubProvider({
      clientId: githubClientId,
      clientSecret: githubClientSecret,
    }),
  ],
  callbacks: {
    async jwt({ token, account, profile }) {
      // Store extended profile data and provider information in JWT token
      if (account && profile) {
        // Store the provider information
        token.provider = account.provider;

        if (account.provider === 'google') {
          const googleProfile = profile as GoogleProfile;
          token.given_name = googleProfile.given_name;
          token.family_name = googleProfile.family_name;
          token.picture = googleProfile.picture || token.picture;
        } else if (account.provider === 'github') {
          const githubProfile = profile as GitHubProfile;
          // GitHub doesn't provide given_name/family_name, so parse the name if available
          const nameParts = githubProfile.name?.split(' ') || [];
          token.given_name = nameParts[0] || undefined;
          token.family_name = nameParts.slice(1).join(' ') || undefined;
          token.picture = githubProfile.avatar_url || token.picture;
        }
      }
      return token;
    },
    async session({ session, token }) {
      // Include extended profile data and provider information in session
      return {
        ...session,
        user: {
          ...session.user,
          given_name: token.given_name as string | undefined,
          family_name: token.family_name as string | undefined,
          picture: token.picture as string | undefined,
          provider: token.provider as string | undefined,
        }
      };
    },
  },
};
