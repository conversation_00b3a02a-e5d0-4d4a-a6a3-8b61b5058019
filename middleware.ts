import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

// Protected routes that require authentication
const protectedRoutes = ['/dashboard'];
// Public routes that should redirect to dashboard if already authenticated
const publicAuthRoutes = ['/login'];

export async function middleware(req: NextRequest) {
  const path = req.nextUrl.pathname;

  // Use NextAuth JWT token for authentication
  const token = await getToken({
    req,
    secret: process.env.NEXTAUTH_SECRET
  });
  const isAuthenticated = !!token;

  // Redirect to login if accessing protected route without authentication
  if (protectedRoutes.some(route => path.startsWith(route)) && !isAuthenticated) {
    return NextResponse.redirect(new URL('/login', req.url));
  }

  // Redirect to dashboard if accessing login page while authenticated
  if (publicAuthRoutes.includes(path) && isAuthenticated) {
    const response = NextResponse.redirect(new URL('/dashboard', req.url));
    // Add cache control headers to prevent caching of redirects
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
    return response;
  }

  // For login page, add no-cache headers when not authenticated
  if (publicAuthRoutes.includes(path) && !isAuthenticated) {
    const response = NextResponse.next();
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
    return response;
  }

  return NextResponse.next();
}

// Configure which routes middleware should run on
export const config = {
  matcher: [
    // Include the specific paths you need middleware for
    '/dashboard',
    '/dashboard/:path*',
    '/login',
    '/profile',
    '/profile/:path*'
    // Add other paths as needed
  ],
};


