@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Logout button specific styles - Maximum specificity to override any conflicts */
form button[type="submit"].logout-button {
  background-color: #dc2626 !important;
  background: #dc2626 !important;
  color: #ffffff !important;
  border: none !important;
  border-color: transparent !important;
  outline: none;
}

form button[type="submit"].logout-button:hover {
  background-color: #b91c1c !important;
  background: #b91c1c !important;
  color: #ffffff !important;
}

form button[type="submit"].logout-button:focus {
  background-color: #dc2626 !important;
  background: #dc2626 !important;
  color: #ffffff !important;
}

@media (prefers-color-scheme: dark) {
  form button[type="submit"].logout-button {
    background-color: #b91c1c !important;
    background: #b91c1c !important;
    color: #ffffff !important;
  }

  form button[type="submit"].logout-button:hover {
    background-color: #991b1b !important;
    background: #991b1b !important;
    color: #ffffff !important;
  }

  form button[type="submit"].logout-button:focus {
    background-color: #b91c1c !important;
    background: #b91c1c !important;
    color: #ffffff !important;
  }
}

/* Additional reset for any inherited styles */
button[type="submit"] {
  color: inherit;
}

/* Force styles with attribute selector for maximum compatibility */
button[data-logout="true"] {
  background-color: #dc2626 !important;
  background: #dc2626 !important;
  color: #ffffff !important;
  border: none !important;
}

button[data-logout="true"]:hover {
  background-color: #b91c1c !important;
  background: #b91c1c !important;
}

/* Default: Hide landscape layout (for portrait orientation) */
.mobile-landscape-flex {
  display: none !important;
}

/* Landscape orientation specific styles for mobile login page */
@media (max-width: 1023px) and (orientation: landscape) {
  .mobile-landscape-flex {
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    min-height: 100vh !important;
    padding: 1rem !important;
  }

  .mobile-landscape-hidden {
    display: none !important;
  }

  .mobile-landscape-compact {
    margin-bottom: 1.5rem !important;
  }

  .mobile-landscape-compact h2 {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .mobile-landscape-compact p {
    font-size: 0.875rem !important;
    margin-bottom: 1rem !important;
  }

  .mobile-landscape-footer {
    margin-top: 1.5rem !important;
  }
}
