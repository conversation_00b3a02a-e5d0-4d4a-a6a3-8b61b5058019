'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import { signIn } from 'next-auth/react';

/**
 * Custom hook for managing OAuth button state with automatic reset functionality.
 * 
 * This hook handles:
 * - Loading state management with 500ms delay
 * - <PERSON><PERSON> disabled state to prevent multiple clicks
 * - Automatic reset when user navigates back to the page after starting OAuth flow
 * - Cleanup of timers and event listeners
 * 
 * @param provider - The OAuth provider name (e.g., 'google', 'github')
 * @param callbackUrl - The URL to redirect to after successful authentication
 * @returns Object containing isDisabled, showLoading states and handleSignIn function
 */
export function useOAuthButtonState(provider: string, callbackUrl: string = '/dashboard') {
  const [isDisabled, setIsDisabled] = useState(false);
  const [showLoading, setShowLoading] = useState(false);
  const loadingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const loadingStartTimeRef = useRef<number | null>(null);

  // Reset function to clear all states and timers
  const resetStates = useCallback(() => {
    setIsDisabled(false);
    setShowLoading(false);
    loadingStartTimeRef.current = null;
    if (loadingTimerRef.current) {
      clearTimeout(loadingTimerRef.current);
      loadingTimerRef.current = null;
    }
  }, []);

  // Set up event listeners for page visibility and focus changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      // Only reset if page becomes visible and loading has been active for >2 seconds
      // This prevents false positives from quick tab switches
      if (document.visibilityState === 'visible' && loadingStartTimeRef.current) {
        const loadingDuration = Date.now() - loadingStartTimeRef.current;
        if (loadingDuration > 2000) {
          resetStates();
        }
      }
    };

    const handleFocus = () => {
      // Reset on window focus if loading has been active for >2 seconds
      // This handles cases where user navigates back via browser controls
      if (loadingStartTimeRef.current) {
        const loadingDuration = Date.now() - loadingStartTimeRef.current;
        if (loadingDuration > 2000) {
          resetStates();
        }
      }
    };

    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    // Cleanup function
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      if (loadingTimerRef.current) {
        clearTimeout(loadingTimerRef.current);
      }
    };
  }, [resetStates]);

  // Handle OAuth sign-in with loading state management
  const handleSignIn = useCallback(() => {
    if (isDisabled) return; // Prevent multiple clicks

    // Immediately disable button and record start time
    setIsDisabled(true);
    loadingStartTimeRef.current = Date.now();
    
    // Set a 500ms delay before showing loading spinner
    loadingTimerRef.current = setTimeout(() => {
      setShowLoading(true);
    }, 500);

    // Initiate OAuth flow
    signIn(provider, { callbackUrl });
  }, [isDisabled, provider, callbackUrl]);

  return {
    isDisabled,
    showLoading,
    handleSignIn
  };
}
