import NextAuth from 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      given_name?: string;
      family_name?: string;
      picture?: string;
      provider?: string;
    };
  }

  interface JWT {
    given_name?: string;
    family_name?: string;
    picture?: string;
    provider?: string;
  }
}
